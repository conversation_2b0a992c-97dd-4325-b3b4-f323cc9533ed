# == Schema Information
#
# Table name: firmwares
#
#  id             :bigint           not null, primary key
#  deploy_env     :json
#  description_en :text(65535)
#  description_ja :text(65535)
#  description_ko :text(65535)
#  description_pt :text(65535)
#  description_tw :text(65535)
#  description_zh :text(65535)
#  file           :string(255)
#  force_update   :boolean
#  title_en       :string(255)
#  title_ja       :string(255)
#  title_ko       :string(255)
#  title_tw       :string(255)
#  title_zh       :string(255)
#  version        :string(255)
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  device_id      :integer
#
FactoryBot.define do
  factory :firmware do
    version { "MyString" }
    force_update { false }
    file { "MyString" }
    title_zh { "MyString" }
    description_zh { "MyText" }
    title_en { "MyString" }
    description_en { "MyText" }
    title_tw { "MyString" }
    description_tw { "MyText" }
    title_ja { "MyString" }
    description_ja { "MyText" }
    title_ko { "MyString" }
    description_ko { "MyText" }
  end
end

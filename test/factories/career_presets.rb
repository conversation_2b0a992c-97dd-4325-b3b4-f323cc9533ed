# == Schema Information
#
# Table name: career_presets
#
#  id             :bigint           not null, primary key
#  background     :string(255)
#  deploy_env     :integer
#  description_en :text(65535)
#  description_ja :text(65535)
#  description_ko :text(65535)
#  description_pt :text(65535)
#  description_tw :text(65535)
#  description_zh :text(65535)
#  title_en       :string(255)
#  title_ja       :string(255)
#  title_ko       :string(255)
#  title_pt       :string(255)
#  title_tw       :string(255)
#  title_zh       :string(255)
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#
FactoryBot.define do
  factory :career_preset do
    background { "MyString" }
    title_zh { "MyString" }
    description_zh { "MyText" }
    title_en { "MyString" }
    description_en { "MyText" }
    title_tw { "MyString" }
    description_tw { "MyText" }
    title_ja { "MyString" }
    description_ja { "MyText" }
    title_ko { "MyString" }
    description_ko { "MyText" }
  end
end

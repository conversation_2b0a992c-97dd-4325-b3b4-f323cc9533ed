<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="<%= operation_tokens_path %>">通行秘钥管理</a></li>
            <li class="breadcrumb-item active">编辑通行秘钥</li>
          </ol>
        </div>
        <h4 class="page-title">编辑通行秘钥</h4>
      </div>
    </div>
  </div>
  <!-- end page title -->

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <%= form_with model: [:operation, @token], local: true do |form| %>
            <% if @token.errors.any? %>
              <div class="alert alert-danger">
                <h4><%= pluralize(@token.errors.count, "error") %> prohibited this token from being saved:</h4>
                <ul>
                  <% @token.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            <% end %>

            <div class="mb-3">
              <%= form.label :device_id, "键盘型号", class: "form-label" %>
              <%= form.select :device_id,
                  options_from_collection_for_select(@devices, :id, :name, @token.device_id),
                  { prompt: "请选择键盘型号" },
                  { class: "form-select" } %>
              <div class="form-text">选择对应的键盘型号，系统将自动生成对应的分享链接。</div>
            </div>

            <div class="mb-3">
              <%= form.label :description, "描述", class: "form-label" %>
              <%= form.text_area :description, class: "form-control", rows: 3,
                  placeholder: "请输入通行秘钥的用途描述..." %>
              <div class="form-text">请简要描述这个通行秘钥的用途，方便后续管理。</div>
            </div>

            <div class="mb-3">
              <%= form.label :expired_at, "过期时间", class: "form-label" %>
              <div class="row">
                <div class="col-md-6">
                  <%= form.datetime_local_field :expired_at, class: "form-control",
                      value: @token.expired_at&.strftime("%Y-%m-%dT%H:%M"), id: "token_expired_at" %>
                </div>
                <div class="col-md-6">
                  <select class="form-select" id="expiry_preset" onchange="setExpiryTime()">
                    <option value="">选择预设时间</option>
                    <% Token.expiry_options.each do |label, duration| %>
                      <option value="<%= duration.from_now.strftime('%Y-%m-%dT%H:%M') %>"><%= label %></option>
                    <% end %>
                  </select>
                </div>
              </div>
              <div class="form-text">设置通行秘钥的过期时间，过期后将无法使用。可选择预设时间或自定义时间。</div>
            </div>

            <div class="mb-3">
              <label class="form-label">通行秘钥</label>
              <div class="input-group">
                <input type="text" class="form-control" value="<%= @token.password %>" readonly id="tokenPassword">
                <button class="btn btn-outline-secondary" type="button" onclick="copyPassword()">
                  <i class="mdi mdi-content-copy"></i> 复制
                </button>
              </div>
              <div class="form-text">通行秘钥创建后无法修改。</div>
            </div>

            <div class="mb-3">
              <label class="form-label">分享链接</label>
              <div class="input-group">
                <input type="text" class="form-control" value="<%= @token.share_url %>" readonly id="shareUrl">
                <button class="btn btn-outline-secondary" type="button" onclick="copyShareUrl()">
                  <i class="mdi mdi-content-copy"></i> 复制
                </button>
              </div>
              <div class="form-text">可以直接分享此链接给需要验证的用户。</div>
            </div>

            <div class="alert alert-<%= @token.expired? ? 'danger' : 'success' %>">
              <i class="mdi mdi-<%= @token.expired? ? 'alert-circle' : 'check-circle' %>"></i>
              <strong>状态：</strong>
              <% if @token.expired? %>
                此通行秘钥已过期，无法通过验证
              <% else %>
                此通行秘钥有效，还有 <%= @token.days_until_expiry %> 天过期
              <% end %>
            </div>

            <div class="d-flex justify-content-between">
              <%= link_to "返回", operation_tokens_path, class: "btn btn-secondary" %>
              <%= form.submit "更新通行秘钥", class: "btn btn-primary" %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function copyPassword() {
  const passwordInput = document.getElementById('tokenPassword');
  passwordInput.select();
  passwordInput.setSelectionRange(0, 99999);
  navigator.clipboard.writeText(passwordInput.value).then(function() {
    alert('通行秘钥已复制到剪贴板！');
  });
}

function copyShareUrl() {
  const shareUrlInput = document.getElementById('shareUrl');
  shareUrlInput.select();
  shareUrlInput.setSelectionRange(0, 99999);
  navigator.clipboard.writeText(shareUrlInput.value).then(function() {
    alert('分享链接已复制到剪贴板！');
  });
}

function setExpiryTime() {
  const presetSelect = document.getElementById('expiry_preset');
  const expiredAtInput = document.getElementById('token_expired_at');

  if (presetSelect.value) {
    expiredAtInput.value = presetSelect.value;
  }
}
</script>

<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="<%= operation_tokens_path %>">通行秘钥管理</a></li>
            <li class="breadcrumb-item active">新增通行秘钥</li>
          </ol>
        </div>
        <h4 class="page-title">新增通行秘钥</h4>
      </div>
    </div>
  </div>
  <!-- end page title -->

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <%= form_with model: [:operation, @token], local: true do |form| %>
            <% if @token.errors.any? %>
              <div class="alert alert-danger">
                <h4><%= pluralize(@token.errors.count, "error") %> prohibited this token from being saved:</h4>
                <ul>
                  <% @token.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            <% end %>

            <div class="mb-3">
              <%= form.label :device_id, "键盘型号", class: "form-label" %>
              <%= form.select :device_id,
                  options_from_collection_for_select(@devices, :id, :name, @token.device_id),
                  { prompt: "请选择键盘型号" },
                  { class: "form-select" } %>
              <div class="form-text">选择对应的键盘型号，系统将自动生成对应的分享链接。</div>
            </div>

            <div class="mb-3">
              <%= form.label :description, "描述", class: "form-label" %>
              <%= form.text_area :description, class: "form-control", rows: 3,
                  placeholder: "请输入通行秘钥的用途描述..." %>
              <div class="form-text">请简要描述这个通行秘钥的用途，方便后续管理。</div>
            </div>

            <div class="mb-3">
              <%= form.label :expired_at, "过期时间", class: "form-label" %>
              <div class="row">
                <div class="col-md-6">
                  <%= form.datetime_local_field :expired_at, class: "form-control",
                      value: (Time.current + 1.day).strftime("%Y-%m-%dT%H:%M"), id: "token_expired_at" %>
                </div>
                <div class="col-md-6">
                  <select class="form-select" id="expiry_preset" onchange="setExpiryTime()">
                    <option value="">选择预设时间</option>
                    <% Token.expiry_options.each do |label, duration| %>
                      <option value="<%= duration.from_now.strftime('%Y-%m-%dT%H:%M') %>"><%= label %></option>
                    <% end %>
                  </select>
                </div>
              </div>
              <div class="form-text">设置通行秘钥的过期时间，过期后将无法使用。可选择预设时间或自定义时间。</div>
            </div>

            <div class="alert alert-info">
              <i class="mdi mdi-information"></i>
              <strong>说明：</strong>
              <ul class="mb-0 mt-2">
                <li>通行秘钥将自动生成，无需手动输入</li>
                <li>创建成功后会自动生成分享链接</li>
                <li>您只能管理自己创建的通行秘钥</li>
                <li>过期的通行秘钥将无法通过验证</li>
              </ul>
            </div>

            <div class="d-flex justify-content-between">
              <%= link_to "返回", operation_tokens_path, class: "btn btn-secondary" %>
              <%= form.submit "创建通行秘钥", class: "btn btn-primary" %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function setExpiryTime() {
  const presetSelect = document.getElementById('expiry_preset');
  const expiredAtInput = document.getElementById('token_expired_at');

  if (presetSelect.value) {
    expiredAtInput.value = presetSelect.value;
  }
}
</script>

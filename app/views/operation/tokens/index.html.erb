<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item active">通行秘钥管理</li>
          </ol>
        </div>
        <h4 class="page-title">通行秘钥管理</h4>
      </div>
    </div>
  </div>
  <!-- end page title -->

  <!-- 分享链接提示 -->
  <% if flash[:share_url] %>
    <div class="row">
      <div class="col-12">
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <strong>通行秘钥创建成功！</strong> 分享链接已生成：
          <div class="input-group mt-2">
            <input type="text" class="form-control" id="shareUrl" value="<%= flash[:share_url] %>" readonly>
            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard()">
              <i class="mdi mdi-content-copy"></i> 复制
            </button>
          </div>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      </div>
    </div>
  <% end %>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row mb-2">
            <div class="col-sm-2">
              <%= link_to "新增通行秘钥", new_operation_token_path, class: "btn btn-primary" %>
            </div>
            <div class="col-sm-10">
              <%= form_with url: operation_tokens_path, method: :get, local: true, class: "d-flex" do |form| %>
                <%= form.text_field "q[description_cont]", placeholder: "搜索描述",
                    value: params.dig(:q, :description_cont), class: "form-control me-2" %>
                <%= form.submit "搜索", class: "btn btn-outline-secondary" %>
              <% end %>
            </div>
          </div>
          <div class="table-responsive">
            <table class="table table-centered table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>ID</th>
                  <th>键盘型号</th>
                  <th>描述</th>
                  <th>通行秘钥</th>
                  <th>分享链接</th>
                  <th>过期时间</th>
                  <th>状态</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% @tokens.each do |token| %>
                  <tr>
                    <td><%= token.id %></td>
                    <td>
                      <% if token.device.present? %>
                        <span class="badge bg-info"><%= token.device.name %></span>
                      <% else %>
                        <span class="text-muted">未指定</span>
                      <% end %>
                    </td>
                    <td>
                      <% if token.description.present? %>
                        <%= truncate(token.description, length: 50) %>
                      <% else %>
                        <span class="text-muted">无描述</span>
                      <% end %>
                    </td>
                    <td>
                      <code class="text-muted">
                        <%= token.password[0..7] %>...
                      </code>
                    </td>
                    <td>
                      <div class="input-group input-group-sm">
                        <input type="text" class="form-control" value="<%= token.share_url %>" readonly id="url_<%= token.id %>">
                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="copyUrl('<%= token.id %>')">
                          <i class="mdi mdi-content-copy"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <%= token.expired_at.strftime("%Y-%m-%d %H:%M") if token.expired_at %>
                      <% if token.expired? %>
                        <br><small class="text-danger">已过期</small>
                      <% else %>
                        <br><small class="text-success"><%= token.days_until_expiry %>天后过期</small>
                      <% end %>
                    </td>
                    <td>
                      <% if token.expired? %>
                        <span class="badge bg-danger">已过期</span>
                      <% else %>
                        <span class="badge bg-success">有效</span>
                      <% end %>
                    </td>
                    <td><%= token.created_at.strftime("%Y-%m-%d %H:%M") %></td>
                    <td>
                      <%= link_to "编辑", edit_operation_token_path(token), class: "btn btn-sm btn-outline-primary" %>
                      <%= link_to "删除", operation_token_path(token), method: :delete,
                          class: "btn btn-sm btn-outline-danger",
                          data: { confirm: '确定要删除这个通行秘钥吗？' },
                          style: "margin-left: 5px;" %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
          <% if @pagy %>
            <div class="mt-3">
              <%== pagy_bootstrap_nav(@pagy) %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function copyToClipboard() {
  const shareUrl = document.getElementById('shareUrl');
  shareUrl.select();
  shareUrl.setSelectionRange(0, 99999);
  navigator.clipboard.writeText(shareUrl.value).then(function() {
    alert('分享链接已复制到剪贴板！');
  });
}

function copyUrl(tokenId) {
  const urlInput = document.getElementById('url_' + tokenId);
  urlInput.select();
  urlInput.setSelectionRange(0, 99999);
  navigator.clipboard.writeText(urlInput.value).then(function() {
    alert('分享链接已复制到剪贴板！');
  });
}
</script>

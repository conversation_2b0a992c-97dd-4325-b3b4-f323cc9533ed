<div class="leftside-menu">
  <!-- LOGO -->
  <a href="/operation" class="text-center logo-light" style="padding-top: 10px;">
    <span class="logo-lg">
      <%= image_tag 'logo.png' %>
    </span>
    <span class="logo-sm">
      <%= image_tag 'logo_sm.png' %>
    </span>
  </a>

  <!-- LOGO -->

  <!--菜单面板 -->
  <div class="h-100" id="leftside-menu-container" data-simplebar>
    <!--- Sidemenu -->
    <ul class="side-nav">
      <li class="side-nav-title side-nav-item">菜单</li>
      <% if policy(:main).dashboard? %>
        <li class="side-nav-item <%= 'menuitem-active' if request.path == '/operation' %>">
          <%= link_to "/operation", class: "side-nav-link", "data-turbo": false do %>
            <i class="mdi mdi-view-dashboard-outline"></i>
            <span>数据面板</span>
          <% end %>
        </li>
      <% end %>
      <% if policy(:product).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/products/ %>">
        <a href="/operation/products" class="side-nav-link">
          <i class="uil-store"></i>
          <span>商品管理</span>
        </a>
      </li>
      <% end %>
      <% if policy(:crowd).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/crowds/ %>">
        <a href="/operation/crowds" class="side-nav-link">
          <i class="uil-yen-circle"></i>
          <span>众筹管理</span>
        </a>
      </li>
      <% end %>
      <% if policy(:order).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/orders/ %>">
        <a href="/operation/orders" class="side-nav-link">
          <i class="mdi mdi-clipboard-text-outline"></i>
          <span>订单管理</span>
        </a>
      </li>
      <% end %>
      <% if policy(:banner).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/banners/ %>">
        <a href="/operation/banners" class="side-nav-link">
          <i class="mdi mdi-rocket-launch-outline"></i>
          <span>首页管理</span>
        </a>
      </li>
      <% end %>
      <% if policy(:user).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/users/ %>">
        <a href="/operation/users" class="side-nav-link">
          <i class="uil-users-alt"></i>
          <span>用户管理</span>
        </a>
      </li>
      <% end %>
      <% if policy(:opinion).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/opinions/ %>">
        <a href="/operation/opinions" class="side-nav-link">
          <i class="mdi mdi-message-text-outline"></i>
          <span>反馈建议</span>
        </a>
      </li>
      <% end %>
      <% if policy(:app_version).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/app_versions/ %>">
        <a href="/operation/app_versions" class="side-nav-link">
          <i class="mdi mdi-tablet-android"></i>
          <span>APP 版本</span>
        </a>
      </li>
      <% end %>
      <% if policy(:admin_user).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/admin_users/ %>">
        <a href="/operation/admin_users" class="side-nav-link">
          <i class="ri-user-settings-line"></i>
          <span>运营人员管理</span>
        </a>
      </li>
      <% end %>
      <% if policy(:push_message).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/push_messages/ %>">
        <a href="/operation/push_messages" class="side-nav-link">
          <i class="ri-send-plane-line"></i>
          <span>推送通知</span>
        </a>
      </li>
      <% end %>
      <% if policy(:career_preset).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/career_presets/ %>">
        <a href="/operation/career_presets" class="side-nav-link">
          <i class="ri-settings-4-line"></i>
          <span>职业预设管理</span>
        </a>
      </li>
      <% end %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/devices/ %>">
        <a href="/operation/devices" class="side-nav-link">
          <i class="ri-keyboard-box-line"></i>
          <span>键盘型号管理</span>
        </a>
      </li>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/firmwares/ %>">
        <a href="/operation/firmwares" class="side-nav-link">
          <i class="ri-cpu-line"></i>
          <span>固件管理</span>
        </a>
      </li>
      <% if policy(Token).index? %>
      <li class="side-nav-item <%= 'menuitem-active' if request.url =~ /operation\/tokens/ %>">
        <a href="/operation/tokens" class="side-nav-link">
          <i class="ri-key-2-line"></i>
          <span>通行秘钥管理</span>
        </a>
      </li>
      <% end %>
      <% if policy(:tag).index? || policy(:crowd_status).index? || policy(:manage_role).index? || policy(:system_config).index? || policy(:log).index? %>
        <% settings = request.url =~ /operation\/manage_roles/ || request.url =~ /operation\/crowd_statuses/ || request.url =~ /operation\/tags/ || request.url =~ /operation\/system_configs/ || request.url =~ /login_logs/ %>
        <li class="side-nav-item <%= 'menuitem-active' if settings %>" >
          <a data-bs-toggle="collapse" href="#adminuser" aria-expanded="false" aria-controls="adminuser" class="side-nav-link">
            <i class="ri-settings-4-line"></i>
            <span>设置</span>
            <span class="menu-arrow"></span>
          </a>
          <div class="collapse <%= 'show' if settings %>" id="adminuser">
            <ul class="side-nav-second-level">
              <% if policy(:tag).index? %>
              <li class="<%= 'menuitem-active' if request.url =~ /operation\/tags/ %>">
                <a href="/operation/tags">
                  <span>分类管理</span>
                </a>
              </li>
              <% end %>
              <% if policy(:crowd_status).index? %>
              <li class="<%= 'menuitem-active' if request.url =~ /operation\/crowd_statuses/ %>">
                <a href="/operation/crowd_statuses">
                  <span>众筹状态管理</span>
                </a>
              </li>
              <% end %>
              <% if policy(:manage_role).index? %>
              <li class="<%= 'menuitem-active' if request.url =~ /operation\/manage_roles/ %>">
                <a href="/operation/manage_roles">
                  <span>角色权限管理</span>
                </a>
              </li>
              <% end %>
              <% if policy(:system_config).index? %>
              <li class="<%= 'menuitem-active' if request.url =~ /operation\/system_configs/ %>">
                <a href="/operation/system_configs">
                  <span>系统配置</span>
                </a>
              </li>
              <% end %>
              <% if policy(:log).index? %>
              <li class="<%= 'menuitem-active' if request.url =~ /login_logs/ %>">
                <a href="/operation/logs/login_logs">
                  <span>日志管理</span>
                </a>
              </li>
              <% end %>
            </ul>
          </div>
        </li>
      <% end %>
    </ul>
    <div class="clearfix"></div>
  </div>
</div>

<%# data-sidenav-size="condensed" class="sidebar-enable" %>
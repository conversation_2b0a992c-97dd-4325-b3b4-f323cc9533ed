<div class="container-fluid">
  <!-- start page title -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item active">固件管理</li>
          </ol>
        </div>
        <h4 class="page-title">固件管理</h4>
      </div>
    </div>
  </div>
  <!-- end page title -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <!-- 搜索和筛选区域 -->
          <div class="row mb-3">
            <div class="col-12">
              <%= form_with url: operation_firmwares_path, method: :get, local: true, class: "row g-3" do |form| %>
                <div class="col-md-3">
                  <%= form.select :device_id, options_for_select([['全部设备', '']] + @devices.map { |name, pid| [name.upcase, pid] }, params[:device_id]),
                      {}, { class: "form-select", onchange: "this.form.submit();" } %>
                </div>
                <div class="col-md-3">
                  <%= form.text_field :version, placeholder: "搜索版本号", value: params[:version],
                      class: "form-control" %>
                </div>
                <div class="col-md-3">
                  <%= form.select :deploy_env, options_for_select([['全部环境', '']] + Firmware.deploy_envs.map { |key, value| [key, key] }, params[:deploy_env]),
                      {}, { class: "form-select", onchange: "this.form.submit();" } %>
                </div>
                <div class="col-md-3">
                  <div class="d-flex gap-2">
                    <%= form.submit "搜索", class: "btn btn-outline-primary" %>
                    <%= link_to "重置", operation_firmwares_path, class: "btn btn-outline-secondary" %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="row mb-3">
            <div class="col-sm-6">
              <%= link_to "新增固件", new_operation_firmware_path, class: "btn btn-primary" %>
            </div>
            <div class="col-sm-6 text-end">
              <small class="text-muted">
                <% if @pagy.count <= 10 %>
                  共 <b><%= @pagy.count %></b> 项
                <% else %>
                  <%== pagy_info(@pagy) %>
                <% end %>
              </small>
            </div>
          </div>

          <!-- 固件列表表格 -->
          <div class="table-responsive">
            <table class="table table-centered table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>设备</th>
                  <th>版本</th>
                  <th>发布环境</th>
                  <th>强制更新</th>
                  <th>固件下载</th>
                  <th>描述</th>
                  <th>创建时间</th>
                  <th style="width: 220px;">操作</th>
                </tr>
              </thead>
              <tbody>
                <% if @firmwares.any? %>
                  <% @firmwares.each do |firmware| %>
                    <tr>
                      <td>
                        <% device = Device.find_by(pid: firmware.device_id) %>
                        <span class="badge bg-info"><%= device&.name&.upcase || "设备ID: #{firmware.device_id}" %></span>
                      </td>
                      <td><strong><%= firmware.version %></strong></td>
                      <td>
                        <% if firmware.deploy_env.present? %>
                          <% firmware.deploy_env.reject(&:empty?).each do |env| %>
                            <span class="badge bg-<%= env == 'production' ? 'success' : env == 'beta' ? 'warning' : 'secondary' %> me-1">
                              <%= env.humanize %>
                            </span>
                          <% end %>
                        <% else %>
                          <span class="text-muted">未设置</span>
                        <% end %>
                      </td>
                      <td>
                        <% if firmware.force_update? %>
                          <span class="badge bg-danger">是</span>
                        <% else %>
                          <span class="badge bg-success">否</span>
                        <% end %>
                      </td>
                      <td>
                        <% if firmware.file.present? %>
                          <%= link_to '下载', firmware.file_url, target: '_blank', class: "btn btn-sm btn-outline-info" %>
                        <% else %>
                          <span class="text-muted">未上传</span>
                        <% end %>
                      </td>
                      <td>
                        <% if firmware.description_zh.present? %>
                          <span title="<%= firmware.description_zh %>">
                            <%= truncate(firmware.description_zh, length: 50) %>
                          </span>
                        <% else %>
                          <span class="text-muted">无描述</span>
                        <% end %>
                      </td>
                      <td>
                        <small class="text-muted">
                          <%= firmware.created_at.strftime("%Y-%m-%d %H:%M") %>
                        </small>
                      </td>
                      <td>
                        <div class="d-flex gap-1">
                          <%= link_to "编辑", edit_operation_firmware_path(firmware),
                              class: "btn btn-sm btn-outline-primary", title: "编辑固件" %>
                          <% if policy(:firmware).audits? %>
                            <%= link_to "审计", audits_operation_firmware_path(firmware),
                                class: "btn btn-sm btn-outline-info", title: "查看审计记录" %>
                            <% end %>
                          <%= link_to "删除", operation_firmware_path(firmware), method: :delete,
                              class: "btn btn-sm btn-outline-danger", title: "删除固件",
                              data: { confirm: "确定删除此固件吗？" } %>
                        </div>
                      </td>
                    </tr>
                  <% end %>
                <% else %>
                  <tr>
                    <td colspan="8" class="text-center text-muted py-4">
                      <i class="mdi mdi-information-outline me-2"></i>
                      暂无固件数据
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>

          <!-- 分页区域 -->
          <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
              <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

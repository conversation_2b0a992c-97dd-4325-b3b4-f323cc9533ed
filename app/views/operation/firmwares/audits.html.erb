<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">首页</a></li>
            <li class="breadcrumb-item"><a href="<%= operation_firmwares_path %>">固件管理</a></li>
            <li class="breadcrumb-item active">审计记录</li>
          </ol>
        </div>
        <h4 class="page-title">固件发布审计记录</h4>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="card-title mb-0">
              固件: <%= @firmware.version %>
              <span class="badge bg-info"><%= Device.find_by(pid: @firmware.device_id)&.name || "未知设备" %></span>
            </h5>
            <%= link_to '返回固件列表', operation_firmwares_path, class: 'btn btn-secondary' %>
          </div>

          <div class="table-responsive">
            <table class="table table-centered table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>操作时间</th>
                  <th>凶手</th>
                  <th>操作类型</th>
                  <th>变更字段</th>
                  <th>变更内容</th>
                  <th>IP地址</th>
                </tr>
              </thead>
              <tbody>
                <% @audits.each do |audit| %>
                  <tr>
                    <td>
                      <div><%= audit.created_at.strftime("%Y-%m-%d %H:%M:%S") %></div>
                      <small class="text-muted"><%= time_ago_in_words(audit.created_at) %>前</small>
                    </td>
                    <td>
                      <% if audit.user %>
                        <div><%= audit.user.name || audit.user.account %></div>
                      <% else %>
                        <span class="text-muted">系统</span>
                      <% end %>
                    </td>
                    <td>
                      <% case audit.action %>
                      <% when 'create' %>
                        <span class="badge bg-success">创建</span>
                      <% when 'update' %>
                        <span class="badge bg-warning">更新</span>
                      <% when 'destroy' %>
                        <span class="badge bg-danger">删除</span>
                      <% else %>
                        <span class="badge bg-secondary"><%= audit.action %></span>
                      <% end %>
                    </td>
                    <td>
                      <% if audit.audited_changes.present? %>
                        <% audit.audited_changes.keys.each do |field| %>
                          <span class="badge bg-light text-dark me-1"><%= field_name_cn(field) %></span>
                        <% end %>
                      <% else %>
                        <span class="text-muted">-</span>
                      <% end %>
                    </td>
                    <td>
                      <% if audit.audited_changes.present? %>
                        <% audit.audited_changes.each do |field, changes| %>
                          <div class="mb-1">
                            <strong><%= field_name_cn(field) %>:</strong>
                            <%# 新建时 changes 不是数组，而是单值 %>
                            <% if audit.action == 'create' %>
                              <% value = changes %>
                              <% if field == 'deploy_env' %>
                                <span class="text-success">
                                  <%= format_deploy_env(value) %>
                                </span>
                              <% elsif field == 'force_update' %>
                                <span class="text-success">
                                  <%= value ? '是' : '否' %>
                                </span>
                              <% elsif field == 'device_id' %>
                                <span class="text-success">
                                  <%= Device.find_by(pid: value)&.name || value %>
                                </span>
                              <% elsif field == 'file' %>
                                <% if value.present? %>
                                  <span class="text-success">
                                    <%= link_to File.basename(value.to_s), 'https://iqunix.oss-cn-shenzhen.aliyuncs.com/uploads/' + value.to_s, target: "_blank", download: true, class: "text-decoration-underline" %>
                                  </span>
                                <% else %>
                                  <span class="text-muted">无附件</span>
                                <% end %>
                              <% else %>
                                <span class="text-success">
                                  <%= truncate(value.to_s, length: 30) %>
                                </span>
                              <% end %>
                            <% else %>
                              <%# 更新时 changes 是 [old, new] 数组 %>
                              <% old_value, new_value = changes %>
                              <% if field == 'deploy_env' %>
                                <div class="d-flex align-items-center">
                                  <span class="text-muted me-2">
                                    <%= format_deploy_env(old_value) %>
                                  </span>
                                  <i class="mdi mdi-arrow-right text-primary me-2"></i>
                                  <span class="text-success">
                                    <%= format_deploy_env(new_value) %>
                                  </span>
                                </div>
                              <% elsif field == 'force_update' %>
                                <div class="d-flex align-items-center">
                                  <span class="text-muted me-2">
                                    <%= old_value ? '是' : '否' %>
                                  </span>
                                  <i class="mdi mdi-arrow-right text-primary me-2"></i>
                                  <span class="text-success">
                                    <%= new_value ? '是' : '否' %>
                                  </span>
                                </div>
                              <% elsif field == 'device_id' %>
                                <div class="d-flex align-items-center">
                                  <span class="text-muted me-2">
                                    <%= Device.find_by(pid: old_value)&.name || old_value %>
                                  </span>
                                  <i class="mdi mdi-arrow-right text-primary me-2"></i>
                                  <span class="text-success">
                                    <%= Device.find_by(pid: new_value)&.name || new_value %>
                                  </span>
                                </div>
                              <% elsif field == 'file' %>
                                <div class="d-flex align-items-center">
                                  <%# 旧附件 %>
                                  <% if old_value.present? %>
                                    <span class="text-muted me-2">
                                      <%= link_to File.basename(old_value.to_s), 'https://iqunix.oss-cn-shenzhen.aliyuncs.com/uploads/' + old_value.to_s, target: "_blank", download: true, class: "text-decoration-underline" %>
                                    </span>
                                  <% else %>
                                    <span class="text-muted me-2">无附件</span>
                                  <% end %>
                                  <i class="mdi mdi-arrow-right text-primary me-2"></i>
                                  <%# 新附件 %>
                                  <% if new_value.present? %>
                                    <span class="text-success">
                                      <%= link_to File.basename(new_value.to_s), 'https://iqunix.oss-cn-shenzhen.aliyuncs.com/uploads/' + new_value.to_s, target: "_blank", download: true, class: "text-decoration-underline" %>
                                    </span>
                                  <% else %>
                                    <span class="text-success">无附件</span>
                                  <% end %>
                                </div>
                              <% else %>
                                <div class="d-flex align-items-center">
                                  <span class="text-muted me-2">
                                    <%= truncate(old_value.to_s, length: 30) %>
                                  </span>
                                  <i class="mdi mdi-arrow-right text-primary me-2"></i>
                                  <span class="text-success">
                                    <%= truncate(new_value.to_s, length: 30) %>
                                  </span>
                                </div>
                              <% end %>
                            <% end %>
                          </div>
                        <% end %>
                      <% else %>
                        <span class="text-muted">首次创建</span>
                      <% end %>
                    </td>
                    <td>
                      <%= audit.remote_address || '-' %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>

          <% if @audits.empty? %>
            <div class="text-center py-4">
              <i class="mdi mdi-file-document-outline text-muted" style="font-size: 48px;"></i>
              <p class="text-muted mt-2">暂无审计记录</p>
            </div>
          <% end %>

          <div class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
              共 <%= @audits.count %> 条记录
            </div>
            <%== pagy_bootstrap_nav(@pagy) if @pagy.pages > 1 %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<% content_for :page_specific_javascript do %>
<script>
  // 可以添加一些页面特定的JavaScript
</script>
<% end %>

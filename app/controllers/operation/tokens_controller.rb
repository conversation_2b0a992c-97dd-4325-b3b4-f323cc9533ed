module Operation
  class TokensController < OperationController
    before_action do
      authorize Token
    end

    before_action :set_token, only: [:show, :edit, :update, :destroy]

    def index
      # 用户只能查看自己创建的token
      @q = current_admin_user_tokens.ransack(params[:q])
      @q.sorts = 'created_at desc' if @q.sorts.empty?
      tokens = @q.result(distinct: true)
      @pagy, @tokens = pagy(tokens)
    end

    def show
    end

    def new
      @token = Token.new
      @devices = Device.all.order(:name)
    end

    def create
      @token = Token.new(token_params)
      @token.user_id = current_admin_user.id.to_s

      if @token.save!
        # 创建成功后显示分享链接
        flash[:success] = "通行秘钥创建成功！"
        flash[:share_url] = @token.share_url
        redirect_to operation_tokens_path
      else
        render :new
      end
    end

    def edit
      @devices = Device.all.order(:name)
    end

    def update
      if @token.update(token_params)
        redirect_to operation_tokens_path, notice: "通行秘钥更新成功！"
      else
        render :edit
      end
    end

    def destroy
      if @token.destroy
        redirect_to operation_tokens_path, notice: "通行秘钥删除成功！"
      else
        redirect_to operation_tokens_path, alert: "通行秘钥删除失败！"
      end
    end

    private

    def set_token
      @token = current_admin_user_tokens.find(params[:id])
    end

    def current_admin_user_tokens
      Token.where(user_id: current_admin_user.id.to_s)
    end

    def token_params
      params.require(:token).permit(:description, :expired_at, :device_id)
    end
  end
end

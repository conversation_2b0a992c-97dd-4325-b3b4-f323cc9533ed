# frozen_string_literal: true

class FirmwarePolicy < ApplicationPolicy
  def index?
    check_auth("firmwares", "index")
  end

  def new?
    create?
  end

  def create?
    check_auth("firmwares", "create")
  end

  def update?
    check_auth("firmwares", "update")
  end

  def destroy?
    check_auth("firmwares", "destroy")
  end

  def show?
    index?
  end

  def audits?
    check_auth("firmwares", "audits")
  end
end

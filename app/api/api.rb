require "grape-swagger"
require "grape_logging"

class API < Grape::API
  helpers ApplicationHelper
  helpers ImageProcessing

  format :json
  content_type :json, "application/json; charset=utf-8"
  prefix :api

  unless Rails.env.test?
    logger.formatter = GrapeLogging::Formatters::Rails.new
    insert_before Grape::Middleware::Error, GrapeLogging::Middleware::RequestLogger, logger: logger
  end

  mount AfterSales
  mount AppVersions
  mount Cities
  mount Comments
  mount Concerns
  mount Crowds
  mount Notifications
  mount Opinions
  mount Orders
  mount Products
  mount Receives
  mount Reports
  mount Sales
  mount Sessions
  mount Shops
  mount Users
  mount CareerPresets
  mount FirmwareCheck
  mount Profiles
  mount VerifyToken

  add_swagger_documentation
end

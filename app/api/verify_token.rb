class VerifyToken < Base
  desc "验证通行秘钥"
  params do
    requires :token, type: String, desc: "通行秘钥"
  end
  get "verify_token" do
    token_password = params[:token]

    if token_password.blank?
      return {
        success: false,
        message: "通行秘钥不能为空",
        code: 400
      }
    end

    # 查找对应的token记录
    token_record = Token.find_by(password: token_password)

    if token_record.nil?
      return {
        success: false,
        message: "通行秘钥不存在",
        code: 404
      }
    end

    # 检查是否过期
    if token_record.expired?
      return {
        success: false,
        message: "通行秘钥已过期",
        code: 410,
        expired_at: token_record.expired_at
      }
    end

    # 验证成功
    {
      success: true,
      message: "通行秘钥验证成功",
      code: 200,
      data: {
        token_id: token_record.id,
        description: token_record.description,
        expired_at: token_record.expired_at,
        days_until_expiry: token_record.days_until_expiry
      }
    }
  end
end

# == Schema Information
#
# Table name: career_presets
#
#  id             :bigint           not null, primary key
#  background     :string(255)
#  deploy_env     :integer
#  description_en :text(65535)
#  description_ja :text(65535)
#  description_ko :text(65535)
#  description_pt :text(65535)
#  description_tw :text(65535)
#  description_zh :text(65535)
#  title_en       :string(255)
#  title_ja       :string(255)
#  title_ko       :string(255)
#  title_pt       :string(255)
#  title_tw       :string(255)
#  title_zh       :string(255)
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#
class CareerPreset < ApplicationRecord
  has_many :keycap_configs, dependent: :destroy
  has_many :keycaps, through: :keycap_configs
  accepts_nested_attributes_for :keycap_configs, allow_destroy: true

  validates :background, :title_zh, :description_zh, presence: true

  mount_uploader :background, BackgroundUploader

  enum deploy_env: {
    dev: 1,
    alpha: 2,
    beta: 3,
    production: 4
  }
end

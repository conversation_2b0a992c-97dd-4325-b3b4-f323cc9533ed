# == Schema Information
#
# Table name: tokens
#
#  id          :bigint           not null, primary key
#  description :text(65535)
#  expired_at  :datetime
#  password    :string(255)      not null
#  share_url   :string(255)
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  device_id   :integer
#  user_id     :string(255)      not null
#
# Indexes
#
#  index_tokens_on_device_id   (device_id)
#  index_tokens_on_expired_at  (expired_at)
#  index_tokens_on_password    (password)
#  index_tokens_on_user_id     (user_id)
#
class Token < ApplicationRecord
  belongs_to :device, optional: true

  validates :user_id, presence: true
  validates :expired_at, presence: true

  before_create :generate_password
  before_create :generate_share_url

  scope :valid_tokens, -> { where('expired_at > ?', Time.current) }
  scope :expired_tokens, -> { where('expired_at <= ?', Time.current) }

  def expired?
    expired_at <= Time.current
  end

  def token_valid?
    !expired?
  end

  def days_until_expiry
    return 0 if expired?
    ((expired_at - Time.current) / 1.day).ceil
  end

  private

  def generate_password
    self.password = SecureRandom.alphanumeric(32) if password.blank?
  end

  def generate_share_url
    # 根据键盘型号生成不同的分享链接
    if device.present?
      # 根据设备名称生成特定的URL路径
      if device.name.downcase.include?('ez')
        self.share_url = "https://ez-alpha.iqunix.com?token=#{password}"
      elsif device.name.downcase.include?('ev')
        self.share_url = "https://ev-alpha.iqunix.com?token=#{password}"
      elsif device.name.downcase.include?('ec')
        self.share_url = "https://ec-alpha.iqunix.com?token=#{password}"
      end
    end
  end

  # 过期时间预设选项
  def self.expiry_options
    [
      ['1小时', 1.hour],
      ['6小时', 6.hours],
      ['1天', 1.day],
      ['1周', 1.week],
      ['1个月', 1.month]
    ]
  end

  def self.ransackable_attributes(auth_object = nil)
    ["created_at", "description", "expired_at", "id", "password", "share_url", "updated_at", "user_id", "device_id"]
  end
end

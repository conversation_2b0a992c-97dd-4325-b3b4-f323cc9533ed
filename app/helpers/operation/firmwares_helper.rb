module Operation::FirmwaresHelper
  # 字段名称中文映射
  def field_name_cn(field)
    field_names = {
      'version' => '版本号',
      'deploy_env' => '发布环境',
      'force_update' => '强制更新',
      'file' => '固件文件',
      'device_id' => '设备型号',
      'description_zh' => '中文描述',
      'description_en' => '英文描述',
      'description_tw' => '繁体中文描述',
      'description_ja' => '日文描述',
      'description_ko' => '韩文描述',
      'description_pt' => '葡萄牙语描述',
      'title_zh' => '中文标题',
      'title_en' => '英文标题',
      'title_tw' => '繁体中文标题',
      'title_ja' => '日文标题',
      'title_ko' => '韩文标题'
    }
    field_names[field] || field
  end

  # 格式化发布环境显示
  def format_deploy_env(env_array)
    return '无' if env_array == [""]

    env_names = {
      'dev' => '开发环境',
      'alpha' => 'Alpha环境',
      'beta' => 'Beta环境',
      'production' => '生产环境'
    }

    # 处理格式问题，去除多余逗号和空白
    envs = if env_array.is_a?(Array)
      env_array.reject(&:blank?).map { |env| env_names[env] || env }
    else
      [env_names[env_array] || env_array]
    end

    envs.reject(&:blank?).join(', ')
  end

  # 审计操作类型的中文显示
  def audit_action_cn(action)
    actions = {
      'create' => '创建',
      'update' => '更新',
      'destroy' => '删除'
    }
    actions[action] || action
  end

  # 审计操作类型的样式类
  def audit_action_class(action)
    classes = {
      'create' => 'bg-success',
      'update' => 'bg-warning',
      'destroy' => 'bg-danger'
    }
    classes[action] || 'bg-secondary'
  end
end

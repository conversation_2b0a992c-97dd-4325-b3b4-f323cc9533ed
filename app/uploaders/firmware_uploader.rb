class FirmwareUploader < CarrierWave::Uploader::Base
  def store_dir
    "uploads"
  end

  def filename
    if original_filename.present?
      ext = File.extname(original_filename)
      base = File.basename(original_filename, ext)
      "files/#{Time.now.strftime("%Y%m")}/#{base}_#{secure_token}#{ext}"
    end
  end

  protected

  # Generate a random key like ActiveStoreage
  def secure_token
    return SecureRandom.base58(32) if model.nil?
    var = :"@#{mounted_as}_secure_token"
    model.instance_variable_get(var) || model.instance_variable_set(var, SecureRandom.base58(32))
  end
end

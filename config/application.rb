require_relative "boot"

require "rails/all"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

Rack::Utils.multipart_part_limit = 0

module IqunixService
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    config.time_zone = "Beijing"
    config.i18n.default_locale = "zh-CN"

    # config.autoload_paths << "#{Rails.root}/api"
    # config.eager_load_paths << "#{Rails.root}/lib"
    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
    require_relative '../lib/middleware/silence_api_logger'
    config.middleware.insert_before Rails::Rack::<PERSON><PERSON>, <PERSON><PERSON><PERSON>Logger
  end
end
